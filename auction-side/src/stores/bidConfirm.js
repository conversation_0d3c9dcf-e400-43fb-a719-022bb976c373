import {defineStore} from 'pinia'
import {ref} from 'vue'

export const useBidConfirmStore = defineStore('bidConfirm', () => {
  const showBidResult = ref(false)
  const showBidConfirm = ref(false)
  const agree = ref(false)
  const data = ref([
    {
      exhibitionNo: null,
      exhibitionName: '',
      bidTotalPrice: null,
      bidList: [
        {
          exhibitionItemNo: null,
          bidQuantity: null,
          bidPrice: null,
          bidTotalPrice: null,
          freeField: {
            product_name: '',
            sim: '',
            capacity: '',
            color: '',
            rank: '',
          },
        },
      ],
    },
  ])

  const setShowBidResult = val => {
    showBidResult.value = val
  }

  const toggleBidConfirm = () => {
    showBidConfirm.value = !showBidConfirm.value
  }

  const resetParams = () => {
    showBidResult.value = false
    showBidConfirm.value = false
    agree.value = false
    data.value = []
  }

  return {
    showBidConfirm,
    showBidResult,
    agree,
    data,
    toggleBidConfirm,
    setShowBidResult,
    resetParams,
  }
})
