/**
 * Type-safe translation composable for Vue 3
 * Provides autocomplete and type checking for translation keys
 */

import { useLocale } from 'vuetify'
import type { TranslationKey, TranslateFunction } from '../language/types'

/**
 * Composable that provides type-safe translation function
 * @returns Object with typed translation function
 */
export function useTypedTranslation() {
  const { t: vuetifyT, current } = useLocale()

  /**
   * Type-safe translation function
   * @param key - Translation key (autocomplete enabled)
   * @returns Translated string
   */
  const t: TranslateFunction = (key: TranslationKey): string => {
    return vuetifyT(key)
  }

  return {
    t,
    current,
    locale: current
  }
}

// Export for backward compatibility
export default useTypedTranslation
