import {CLASSIFICATIONS} from '@/defined/const'
import type {ClassificationType} from '@/types/classification'
import {computed, ref} from 'vue'

/**
 * Composable for managing classification switching between ascending and sealed auction types
 *
 * @param defaultClassification - The default classification type (defaults to ASCENDING)
 * @returns Object containing classification state and computed properties
 */
export default function useClassificationSwitch(
  defaultClassification: ClassificationType = CLASSIFICATIONS.ASCENDING
) {
  const currentAuctionType = ref<ClassificationType>(defaultClassification)

  const isAscending = computed(
    () => currentAuctionType.value === CLASSIFICATIONS.ASCENDING
  )
  const isSealed = computed(
    () => currentAuctionType.value === CLASSIFICATIONS.SEALED
  )

  const getClassificationLabel = (
    classificationType: ClassificationType
  ): string => {
    return classificationType === CLASSIFICATIONS.ASCENDING
      ? '競り上がり入札'
      : '封印入札'
  }

  const currentLabel = computed(() =>
    getClassificationLabel(currentAuctionType.value)
  )

  return {
    currentAuctionType,
    isAscending,
    isSealed,
    currentLabel,
  }
}
