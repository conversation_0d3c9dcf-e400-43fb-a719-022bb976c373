import {CLASSIFICATIONS} from '@/defined/const'
import {computed, ref} from 'vue'

/**
 * Composable for managing classification switching between ascending and sealed auction types
 */
export default function useClassificationSwitch(
  defaultClassification = CLASSIFICATIONS.ASCENDING
) {
  const currentAuctionType = ref<typeof CLASSIFICATIONS>(defaultClassification)

  const isAscending = computed(
    () => currentAuctionType.value === CLASSIFICATIONS.ASCENDING
  )
  const isSealed = computed(
    () => currentAuctionType.value === CLASSIFICATIONS.SEALED
  )

  const getClassificationLabel = classificationType => {
    return classificationType === CLASSIFICATIONS.ASCENDING
      ? '競り上がり入札'
      : '封印入札'
  }

  const currentLabel = computed(() =>
    getClassificationLabel(currentAuctionType.value)
  )

  return {
    currentAuctionType,
    isAscending,
    isSealed,
    currentLabel,
  }
}
