/**
 * TypeScript declarations for Vue i18n integration
 * Extends Vuetify's useLocale to provide type safety
 */

import type { TranslationKey } from '../language/types'

declare module 'vuetify' {
  interface LocaleInstance {
    t(key: TranslationKey): string
  }
}

declare module 'vue-i18n' {
  interface VueI18n {
    t(key: TranslationKey): string
  }
}

// Extend global Vue types if needed
declare module '@vue/runtime-core' {
  interface ComponentCustomProperties {
    $t(key: TranslationKey): string
  }
}
