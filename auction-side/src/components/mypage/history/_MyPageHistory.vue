<script setup lang="ts">
  import ClassificationTabs from '@/components/common/ClassificationTabs.vue'
  import useClassificationSwitch from '@/composables/useClassificationSwitch'
  import HistoryAscending from './HistoryAscending.vue'
  import HistorySealed from './HistorySealed.vue'

  // Use the classification switch composable
  const {classification, isAscending} = useClassificationSwitch()
</script>

<template>
  <section id="list">
    <div class="container">
      <h3>落札履歴</h3>

      <!-- 入札切り替え Start -->
      <ClassificationTabs v-model="classification" />
      <!-- 入札切り替え End -->

      <!-- Render component based on classification -->
      <HistoryAscending v-if="isAscending" />
      <HistorySealed v-else />
    </div>
  </section>
</template>
