<script setup lang="ts">
  import ClassificationTabs from '@/components/common/ClassificationTabs.vue'
  import useClassificationSwitch from '@/composables/useClassificationSwitch'
  import BiddingAscending from './BiddingAscending.vue'
  import BiddingSealed from './BiddingSealed.vue'

  // Use the classification switch composable
  const {classification, isAscending} = useClassificationSwitch()
</script>
<template>
  <section id="list">
    <div class="container">
      <h3>入札中</h3>

      <!-- 入札切り替え Start -->
      <ClassificationTabs v-model="classification" />
      <!-- 入札切り替え End -->

      <div class="display-option">
        <div class="refine">
          <div class="count">
            <p><span>3</span>件のオークション</p>
          </div>
        </div>
        <!--
          <div class="switch">
            <div class="number-switch">
              <p class="label">表示件数</p>
              <div class="num">
                <button class="btn is-active">20件</button>
                <button class="btn">50件</button>
                <button class="btn">100件</button>
              </div>
            </div>
          </div>
          -->
      </div>

      <!-- Render component based on classification -->
      <BiddingAscending v-if="isAscending" />
      <BiddingSealed v-else />
    </div>
  </section>
</template>
