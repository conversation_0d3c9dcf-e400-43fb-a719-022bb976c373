<script setup lang="ts">
  import ClassificationTabs from '@/components/common/ClassificationTabs.vue'
  import useClassificationSwitch from '@/composables/useClassificationSwitch'
  import FavoriteAscending from './FavoriteAscending.vue'
  import FavoriteSealed from './FavoriteSealed.vue'

  // Use the classification switch composable
  const {classification, isAscending} = useClassificationSwitch()
</script>

<template>
  <section id="list">
    <div class="container">
      <h3>お気に入り</h3>

      <!-- 入札切り替え Start -->
      <ClassificationTabs v-model="classification" />
      <!-- 入札切り替え End -->

      <!-- Render component based on classification -->
      <FavoriteAscending v-if="isAscending" />
      <FavoriteSealed v-else />
    </div>
  </section>
</template>

<style lang="css" scoped></style>
