<script setup lang="ts">
  import useTypedTranslation from '@/composables/useTypedTranslation'
  import {CLASSIFICATIONS} from '@/defined/const'

  const {t: translate} = useTypedTranslation()
  // Props
  interface Props {
    modelValue: typeof CLASSIFICATIONS
    disabled?: boolean
  }

  const props = withDefaults(defineProps<Props>(), {
    disabled: false,
  })

  // Emits
  const emit = defineEmits<{
    'update:modelValue': [value: string]
  }>()

  // Handle tab click
  const handleTabClick = (classification: string) => {
    if (!props.disabled && props.modelValue !== classification) {
      emit('update:modelValue', classification)
    }
  }

  // Check if tab is active
  const isActive = (classification: string) => {
    return props.modelValue === classification
  }
</script>

<template>
  <div class="bid-tab-wrap">
    <div
      class="tab-cont"
      :class="{
        active: isActive(CLASSIFICATIONS.ASCENDING),
        disabled: disabled,
      }"
      @click="handleTabClick(CLASSIFICATIONS.ASCENDING)"
    >
      <div class="label">{{ translate('CLASSIFICATION_ASCENDING') }}</div>
    </div>
    <div
      class="tab-cont"
      :class="{
        active: isActive(CLASSIFICATIONS.SEALED),
        disabled: disabled,
      }"
      @click="handleTabClick(CLASSIFICATIONS.SEALED)"
    >
      <div class="label">{{ translate('CLASSIFICATION_SEALED') }}</div>
    </div>
  </div>
</template>

<style scoped>
  .tab-cont {
    cursor: pointer;
    transition:
      color 0.15s ease,
      border-color 0.15s ease,
      background-color 0.15s ease;
    transition-property: color, border-color, background-color;
  }

  .tab-cont.disabled {
    cursor: not-allowed;
    opacity: 0.6;
    transition: none;
  }

  .tab-cont.active {
    transition-property: color, border-color, background-color;
  }

  /* Override any global transitions that affect tab switching */
  .bid-tab-wrap .tab-cont,
  .bid-tab-wrap .tab-cont * {
    transition-property: color, border-color, background-color !important;
    transition-duration: 0.15s !important;
  }
</style>
