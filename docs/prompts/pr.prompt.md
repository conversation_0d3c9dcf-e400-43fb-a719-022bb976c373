# PRの文章生成

このプロンプトは日本語を使用してください。
以下にPRの文章生成の手順を示します。

## 1. GitHub Copilotのモード確認

あなたが現在利用可能なツールに、Git操作系（get_changed_filesなど）が含まれているかどうかを確認してください。

### あなたが現在利用可能なツールにGit操作系（get_changed_filesなど）が含まれていないケース

ユーザーにGitHub CopilotをAgentモードに切り替えることを伝えて処理を中断します。

### あなたが現在利用可能なツールにGit操作系（get_changed_filesなど）が含まれているケース

処理を続行してください。

## 2. デフォルトブランチと現在のブランチの差分を確認

以下のコマンドで差分を確認し、どのような変更が行われたかを把握してください。

```bash
git fetch && \
git remote set-head origin -a && \
git diff origin/HEAD
```

<!--
以下はコマンドの解説です。
- git fetch : リモートの最新情報を取得します。
- git remote set-head origin -a : リモートのデフォルトブランチをローカルに設定します。
- git diff origin/HEAD : デフォルトブランチと現在のブランチの差分を表示します。
-->

## 3. 関連のあるIssueを確認

変更内容に関連するGitHubのIssueがあるかどうかを探します。
以下のコマンドを使用してタイトルと内容を含む最新のIssue30件を取得してください。

```bash
gh issue list \
  --limit 30 \
  --state open \
  --json number,title,body \
  --jq '.[] | "Issue #\(.number): \(.title)\n\(.body // "")\n---"'
```

関連するIssueが見つかった場合に、次のステップでIssue番号を記述します。

<!--
以下はコマンドの解説です。
- gh issue list : GitHubのIssue一覧を取得します。
- --limit 30 : 最新30件まで取得します。
- --state open : オープンなIssueのみを対象とします。
- --json number,title,body : Issue番号、タイトル、本文をJSON形式で取得します。
- --jq '.[] | ...' : JSON出力を見やすい形式に整形します。
-->

## 4. PR文章を生成

PR文章の生成は次のルールを守ってください。

- ユーザーがコピペできるようMarkdownコードブロックで囲み出力する
- PR文章は `.github/PULL_REQUEST_TEMPLATE.md` の形式に従う

具体的には次のように記述してください。

```markdown
<!-- ここに `.github/pull_request_template.md` の形式でPR文章を書いてください -->
<!-- `.github/pull_request_template.md` に含まれているコメントはそのまま転記してください。 -->
```
